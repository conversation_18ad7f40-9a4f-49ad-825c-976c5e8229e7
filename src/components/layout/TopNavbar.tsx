import { useState } from "react";
import { Link } from "react-router-dom";
import { cn } from "@/lib/utils";
import {
  UserPlus,
  LayoutDashboard,
  UserCircle,
  Menu,
  LogOut,
  Trophy,
} from "lucide-react";
import { useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import {
  She<PERSON>,
  <PERSON>etContent,
  <PERSON>etHeader,
  SheetTitle,
  SheetTrigger
} from "@/components/ui/sheet";
import GroupSelector from "@/components/GroupSelector";
import ThemeToggle from "@/components/ThemeToggle";
import LanguageToggle from "@/components/LanguageToggle";
import LogoComponent from "@/components/LogoComponent";
import { useAuth } from "@/context/AuthContext";

const getNavigation = (t) => [
  { name: t('nav.dashboard'), href: "/dashboard", icon: LayoutDashboard },
  { name: t('nav.players'), href: "/players", icon: UserPlus },
  { name: t('nav.matches'), href: "/matches", icon: Trophy },
  { name: t('nav.profile'), href: "/profile", icon: UserCircle },
];

const TopNavbar = () => {
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { t } = useTranslation();
  const { signOut } = useAuth();
  const navigation = getNavigation(t);

  // Desktop navigation links with enhanced active states
  const renderDesktopNavLinks = () => (
    navigation.map((item) => (
      <Link
        key={item.name}
        to={item.href}
        className={cn(
          "px-4 py-2 rounded-lg text-sm font-medium flex items-center gap-2 transition-all duration-300 relative group",
          location.pathname === item.href
            ? "bg-white/20 text-white shadow-md border border-white/30 scale-105"
            : "text-white/80 hover:bg-white/10 hover:text-white hover:scale-105 hover:shadow-sm"
        )}
      >
        <item.icon className={cn(
          "w-4 h-4 transition-all duration-300",
          location.pathname === item.href ? "scale-110" : "group-hover:scale-105"
        )} />
        <span className="font-medium">{item.name}</span>
        {location.pathname === item.href && (
          <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-white rounded-full shadow-sm" />
        )}
      </Link>
    ))
  );

  // Mobile navigation links with enhanced active states
  const renderMobileNavLinks = () => (
    navigation.map((item) => (
      <Link
        key={item.name}
        to={item.href}
        onClick={() => setIsMobileMenuOpen(false)}
        className={cn(
          "px-3 py-3 rounded-lg text-sm font-medium flex items-center gap-3 w-full transition-all duration-300 relative group",
          location.pathname === item.href
            ? "bg-white/20 text-white shadow-md border-l-4 border-white scale-105"
            : "text-white/80 hover:bg-white/10 hover:text-white hover:scale-105"
        )}
      >
        <item.icon className={cn(
          "w-5 h-5 transition-all duration-300",
          location.pathname === item.href ? "scale-110" : "group-hover:scale-105"
        )} />
        <span className="font-medium">{item.name}</span>
      </Link>
    ))
  );

  return (
    <>
      {/* Desktop Top Navbar */}
      <nav className="hidden md:block bg-soccer-primary dark:bg-soccer-primary-dark text-white transition-colors duration-200 sticky top-0 z-50 shadow-sm">
        <div className="max-w-[1600px] mx-auto px-8">
          <div className="flex items-center justify-between h-16">
            {/* Left section: Logo and Group Selector */}
            <div className="flex items-center gap-6 min-w-0 flex-shrink-0">
              <LogoComponent to="/dashboard" />
              <div className="min-w-[200px] max-w-[300px]">
                <GroupSelector />
              </div>
            </div>

            {/* Center section: Navigation Links */}
            <div className="flex items-center gap-1 flex-1 justify-center">
              {renderDesktopNavLinks()}
            </div>

            {/* Right section: Profile Actions */}
            <div className="flex items-center gap-4 flex-shrink-0">
              <div className="flex items-center gap-3">
                <LanguageToggle />
                <ThemeToggle />
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="text-white/70 hover:bg-white/5 hover:text-white transition-colors duration-300 ml-2"
                onClick={signOut}
              >
                <LogOut className="h-4 w-4 mr-2" />
                {t('auth.signOut')}
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Header with hamburger menu */}
      <div className="md:hidden bg-soccer-primary dark:bg-soccer-primary-dark text-white p-4 flex items-center justify-between sticky top-0 z-50 shadow-sm transition-colors duration-200">
        <div className="flex items-center gap-4">
          <LogoComponent to="/dashboard" />
        </div>

        <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
          <SheetTrigger asChild>
            <Button variant="ghost" size="icon" className="hover:bg-white/10 focus:bg-white/10">
              <Menu className="h-6 w-6" />
              <span className="sr-only">Open menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="bg-soccer-primary dark:bg-soccer-primary-dark text-white border-soccer-accent/20 pt-10 w-[280px] transition-colors duration-200">
            <SheetHeader className="mb-4 text-left">
              <SheetTitle className="text-white flex items-center">
                <LogoComponent to="/dashboard" />
              </SheetTitle>
            </SheetHeader>
            <div className="mb-6">
              <GroupSelector />
            </div>
            <div className="flex flex-col space-y-2">
              {renderMobileNavLinks()}
            </div>
            <div className="mt-6 pt-6 border-t border-white/10 space-y-4">
              <Link to="/profile" className="block">
                <Button
                  variant="ghost"
                  className="w-full justify-start text-white/70 hover:bg-white/5 hover:text-white"
                >
                  <UserCircle className="h-5 w-5 mr-2" />
                  {t('nav.profile')}
                </Button>
              </Link>
              <div className="flex items-center gap-3">
                <ThemeToggle />
                <span className="text-sm">{t('profile.theme')}</span>
              </div>
              <div className="flex items-center gap-3">
                <LanguageToggle />
                <span className="text-sm">{t('profile.language')}</span>
              </div>
              <Button
                variant="ghost"
                className="w-full justify-start text-white/70 hover:bg-white/5 hover:text-white mt-2"
                onClick={signOut}
              >
                <LogOut className="h-5 w-5 mr-2" />
                {t('auth.signOut')}
              </Button>
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </>
  );
};

export default TopNavbar;
