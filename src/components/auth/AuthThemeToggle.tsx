import ThemeToggle from "@/components/ThemeToggle";
import LanguageToggle from "@/components/LanguageToggle";
import { useTheme } from "@/context/ThemeContext";

const AuthThemeToggle = () => {
  const { theme } = useTheme();
  const textColor = theme === 'dark' ? 'text-white' : 'text-gray-800';
  const hoverBg = theme === 'dark' ? 'hover:bg-white/10' : 'hover:bg-black/10';

  return (
    <div className="absolute top-4 right-4 flex gap-2 z-20">
      <ThemeToggle variant="ghost" className={`bg-transparent ${textColor} ${hoverBg}`} />
      <LanguageToggle variant="ghost" className={`bg-transparent ${textColor} ${hoverBg}`} />
    </div>
  );
};

export default AuthThemeToggle;
