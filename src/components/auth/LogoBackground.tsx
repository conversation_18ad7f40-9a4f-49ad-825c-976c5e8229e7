import React from 'react';
import { cn } from '@/lib/utils';
import { useTheme } from '@/context/ThemeContext';

interface LogoBackgroundProps {
  className?: string;
  children: React.ReactNode;
}

const LogoBackground: React.FC<LogoBackgroundProps> = ({
  className,
  children
}) => {
  const { theme } = useTheme();
  return (
    <div className={cn(
      "min-h-screen relative flex items-center justify-center overflow-hidden",
      theme === 'dark' ? 'bg-soccer-dark-100' : 'bg-gray-50',
      className
    )}>
      {/* Logo background overlay */}
      <div className="absolute inset-0 z-0 flex items-center justify-center">
        <svg
          width="250%"
          height="250%"
          viewBox="0 0 516 516"
          xmlns="http://www.w3.org/2000/svg"
          className="opacity-20"
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)'
          }}
        >
          <path
            d="M507.03,236.67L279.64,8.95C273.88,3.18,266.22,0,258.06,0h-.01c-8.15,0-15.81,3.17-21.57,8.93L58.5,186.92l57.3,57.3,71.13-71.13,17.63,17.63-71.13,71.13,138.48,138.48-13.84,13.84L44.66,200.76l-35.73,35.73c-5.76,5.76-8.93,13.42-8.93,21.57s3.17,15.81,8.93,21.57l227.56,227.56c5.95,5.95,13.76,8.92,21.57,8.92s15.62-2.97,21.57-8.92l106.86-106.86-138.48-138.48,13.84-13.84,138.48,138.48,57.3-57.3L244.22,115.79l13.66-13.66,213.4,213.4,35.73-35.73c11.88-11.89,11.89-31.23.02-43.13Z"
            fill="none"
            stroke={theme === 'dark' ? '#35db71' : '#1a8f45'}
            strokeWidth="4"
          />
        </svg>
      </div>

      {/* Content */}
      <div className="relative z-10 w-full">
        {children}
      </div>
    </div>
  );
};

export default LogoBackground;
