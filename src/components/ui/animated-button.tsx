import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90 active:scale-95 transition-transform duration-200",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90 active:scale-95 transition-transform duration-200",
        outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground active:scale-95 transition-transform duration-200",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80 active:scale-95 transition-transform duration-200",
        ghost: "hover:bg-accent hover:text-accent-foreground active:scale-95 transition-transform duration-200",
        link: "text-primary underline-offset-4 hover:underline",
        success: "bg-green-600 text-white hover:bg-green-700 active:scale-95 transition-transform duration-200",
        warning: "bg-yellow-500 text-white hover:bg-yellow-600 active:scale-95 transition-transform duration-200",
        info: "bg-blue-500 text-white hover:bg-blue-600 active:scale-95 transition-transform duration-200",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
      animation: {
        none: "",
        pulse: "animate-pulse-glow",
        bounce: "hover:animate-bounce",
        spin: "group-hover:animate-spin",
        ping: "hover:animate-ping",
        wiggle: "hover:animate-wiggle",
        scale: "interactive-scale",
        slide: "hover:translate-x-1 animate-smooth",
        glow: "hover:shadow-lg hover:shadow-soccer-primary/25 animate-smooth",
        spring: "animate-spring interactive-scale",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      animation: "none",
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: "left" | "right";
}

const AnimatedButton = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, animation, asChild = false, loading, icon, iconPosition = "left", children, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    
    return (
      <Comp
        className={cn(
          buttonVariants({ variant, size, animation, className }),
          loading && "opacity-70 cursor-not-allowed"
        )}
        ref={ref}
        disabled={props.disabled || loading}
        {...props}
      >
        {loading ? (
          <div className="flex items-center justify-center animate-fade-in">
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span className="animate-pulse">{typeof children === 'string' ? 'Loading...' : children}</span>
          </div>
        ) : (
          <div className="flex items-center justify-center">
            {icon && iconPosition === "left" && <span className="mr-2">{icon}</span>}
            {children}
            {icon && iconPosition === "right" && <span className="ml-2">{icon}</span>}
          </div>
        )}
      </Comp>
    );
  }
);

AnimatedButton.displayName = "AnimatedButton";

export { AnimatedButton, buttonVariants };
