import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, <PERSON>T<PERSON>le, CardFooter } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import AuthThemeToggle from '@/components/auth/AuthThemeToggle';
import { useTheme } from '@/context/ThemeContext';
import { useAuth } from '@/context/AuthContext';
import LogoComponent from '@/components/LogoComponent';
import LogoBackground from '@/components/auth/LogoBackground';
import { useTranslation } from 'react-i18next';

const SignUpPage: React.FC = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const { signUp } = useAuth();
  const navigate = useNavigate();

  // Form state
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  // UI state
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);
    setSuccess(false);

    try {
      // Validate password strength
      if (password.length < 6) {
        throw new Error('Password must be at least 6 characters long');
      }

      // Call the signUp method from AuthContext
      await signUp(email, password, username);

      // Set success state
      setSuccess(true);

      // Redirect to login page after a short delay
      setTimeout(() => {
        navigate('/');
      }, 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to sign up');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <LogoBackground>
      <AuthThemeToggle />
      <div className="w-full max-w-md mx-auto px-4">
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="h-16 w-16">
              <LogoComponent to="/" height="64px" width="64px" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-soccer-primary">{t('app.name')}</h1>
          <p className={`mt-2 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>{t('app.tagline')}</p>
        </div>

        <Card className={`border-0 shadow-xl overflow-hidden ${theme === 'dark' ? 'bg-soccer-dark-200' : 'bg-card'}`}>
          <CardHeader>
            <CardTitle className={`text-xl ${theme === 'dark' ? 'text-white' : 'text-card-foreground'}`}>{t('auth.signup')}</CardTitle>
          </CardHeader>
          <form onSubmit={handleSignUp}>
            <CardContent className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert>
                  <AlertDescription>
                    Account created successfully! Please check your email for a confirmation link. You will be redirected to the login page shortly.
                  </AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <label htmlFor="username" className={`block text-sm font-medium ${theme === 'dark' ? 'text-white' : 'text-card-foreground'}`}>Username</label>
                <Input
                  type="text"
                  id="username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  required
                  placeholder="Enter your username"
                  className={theme === 'dark' ? 'bg-soccer-dark-400 border-soccer-dark-600 text-white placeholder-gray-400' : 'bg-background border-input text-foreground placeholder-gray-400'}
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="email" className={`block text-sm font-medium ${theme === 'dark' ? 'text-white' : 'text-card-foreground'}`}>Email</label>
                <Input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  placeholder="Enter your email"
                  className={theme === 'dark' ? 'bg-soccer-dark-400 border-soccer-dark-600 text-white placeholder-gray-400' : 'bg-background border-input text-foreground placeholder-gray-400'}
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="password" className={`block text-sm font-medium ${theme === 'dark' ? 'text-white' : 'text-card-foreground'}`}>Password</label>
                <Input
                  type="password"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  placeholder="Enter your password"
                  className={theme === 'dark' ? 'bg-soccer-dark-400 border-soccer-dark-600 text-white placeholder-gray-400' : 'bg-background border-input text-foreground placeholder-gray-400'}
                />
                <p className={`text-xs mt-1 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-500'}`}>Password must be at least 6 characters long</p>
              </div>
            </CardContent>
            <CardFooter className="flex-col space-y-4">
              <Button
                type="submit"
                className="w-full bg-soccer-primary hover:bg-soccer-primary/90 text-white font-medium py-6"
                disabled={isLoading || success}
              >
                {isLoading ? "Creating account..." : "Sign Up"}
              </Button>

              <p className={`text-sm text-center ${theme === 'dark' ? 'text-white' : 'text-card-foreground'}`}>
                Already have an account?{" "}
                <Link to="/" className="text-soccer-primary hover:underline font-medium">
                  Log in
                </Link>
              </p>
            </CardFooter>
          </form>
        </Card>
      </div>
    </LogoBackground>
  );
};

export default SignUpPage;