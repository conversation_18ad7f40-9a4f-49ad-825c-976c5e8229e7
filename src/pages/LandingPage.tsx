import { useState, useRef } from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Play, Users, Shuffle, Calendar, Handshake } from "lucide-react";
import LogoComponent from "@/components/LogoComponent";
import LogoBackground from "@/components/auth/LogoBackground";
import AuthThemeToggle from "@/components/auth/AuthThemeToggle";
import { useTheme } from "@/context/ThemeContext";

const LandingPage = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const [isPlaying, setIsPlaying] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  const handlePlayVideo = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const textColor = theme === 'dark' ? 'text-white' : 'text-gray-800';
  const cardBg = theme === 'dark' ? 'bg-gray-800' : 'bg-white';
  const cardHover = theme === 'dark' ? 'hover:bg-gray-700' : 'hover:bg-gray-50';

  return (
    <LogoBackground>
      <AuthThemeToggle />
      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="flex flex-col md:flex-row items-center justify-between gap-8 mb-16">
          <div className="md:w-1/2">
            <div className="flex items-center gap-4 mb-6">
              <div className="h-24 w-24">
                <LogoComponent to="/" height="96px" width="96px" />
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-white">
                {t('app.name')}
              </h1>
            </div>
            <h2 className="text-2xl md:text-3xl font-semibold mb-4 text-soccer-primary">
              {t('landing.subtitle')}
            </h2>
            <p className={`text-lg mb-8 ${textColor}`}>
              {t('landing.description')}
            </p>
            <div className="flex flex-wrap gap-4">
              <Button
                asChild
                className="bg-soccer-primary hover:bg-soccer-primary/90 text-white px-8 py-6 text-lg"
              >
                <Link to="/login">{t('landing.login')}</Link>
              </Button>
              <Button
                asChild
                variant="outline"
                className="border-soccer-primary text-soccer-primary hover:bg-soccer-primary/10 px-8 py-6 text-lg"
              >
                <Link to="/signup">{t('landing.signup')}</Link>
              </Button>
            </div>
          </div>
          <div className="md:w-1/2 relative">
            <div className="aspect-video bg-black rounded-lg overflow-hidden shadow-xl mt-6">
              <video
                ref={videoRef}
                className="w-full h-full object-cover"
                src="/FSTATS SHOWCASE.mp4"
                poster="/assets/video-poster.png"
                controls={isPlaying}
              />
              {!isPlaying && (
                <div
                  className="absolute inset-0 flex items-center justify-center cursor-pointer"
                  onClick={handlePlayVideo}
                >
                  <div className="bg-soccer-primary/80 rounded-full p-4">
                    <Play className="h-12 w-12 text-white" />
                  </div>
                  <span className="absolute bottom-8 text-white font-medium text-lg bg-black/50 px-4 py-2 rounded-full">
                    {t('landing.watchDemo')}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="mb-16">
          <h2 className={`text-3xl font-bold mb-8 text-center ${textColor}`}>
            {t('landing.features')}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className={`p-6 transition-all ${cardBg} ${cardHover}`}>
              <div className="flex flex-col items-center text-center">
                <div className="bg-soccer-primary/10 p-4 rounded-full mb-4">
                  <Users className="h-8 w-8 text-soccer-primary" />
                </div>
                <h3 className={`text-xl font-semibold mb-2 ${textColor}`}>
                  {t('landing.feature1Title')}
                </h3>
                <p className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
                  {t('landing.feature1Description')}
                </p>
              </div>
            </Card>

            <Card className={`p-6 transition-all ${cardBg} ${cardHover}`}>
              <div className="flex flex-col items-center text-center">
                <div className="bg-soccer-primary/10 p-4 rounded-full mb-4">
                  <Shuffle className="h-8 w-8 text-soccer-primary" />
                </div>
                <h3 className={`text-xl font-semibold mb-2 ${textColor}`}>
                  {t('landing.feature2Title')}
                </h3>
                <p className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
                  {t('landing.feature2Description')}
                </p>
              </div>
            </Card>

            <Card className={`p-6 transition-all ${cardBg} ${cardHover}`}>
              <div className="flex flex-col items-center text-center">
                <div className="bg-soccer-primary/10 p-4 rounded-full mb-4">
                  <Calendar className="h-8 w-8 text-soccer-primary" />
                </div>
                <h3 className={`text-xl font-semibold mb-2 ${textColor}`}>
                  {t('landing.feature3Title')}
                </h3>
                <p className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
                  {t('landing.feature3Description')}
                </p>
              </div>
            </Card>

            <Card className={`p-6 transition-all ${cardBg} ${cardHover}`}>
              <div className="flex flex-col items-center text-center">
                <div className="bg-soccer-primary/10 p-4 rounded-full mb-4">
                  <Handshake className="h-8 w-8 text-soccer-primary" />
                </div>
                <h3 className={`text-xl font-semibold mb-2 ${textColor}`}>
                  {t('landing.feature4Title')}
                </h3>
                <p className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
                  {t('landing.feature4Description')}
                </p>
              </div>
            </Card>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <h2 className={`text-3xl font-bold mb-6 ${textColor}`}>
            {t('landing.getStarted')}
          </h2>
          <div className="flex justify-center gap-4">
            <Button
              asChild
              className="bg-soccer-primary hover:bg-soccer-primary/90 text-white px-8 py-6 text-lg"
            >
              <Link to="/login">{t('landing.login')}</Link>
            </Button>
            <Button
              asChild
              variant="outline"
              className="border-soccer-primary text-soccer-primary hover:bg-soccer-primary/10 px-8 py-6 text-lg"
            >
              <Link to="/signup">{t('landing.signup')}</Link>
            </Button>
          </div>
        </div>
      </div>
    </LogoBackground>
  );
};

export default LandingPage;
