import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/context/AuthContext";
import { Alert, AlertDescription } from "@/components/ui/alert";
import AuthThemeToggle from "@/components/auth/AuthThemeToggle";
import LogoComponent from "@/components/LogoComponent";
import LogoBackground from "@/components/auth/LogoBackground";
import { useTheme } from "@/context/ThemeContext";

const ResetPasswordPage = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const [email, setEmail] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const { resetPassword } = useAuth();

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);
    setSuccess(false);

    try {
      await resetPassword(email);
      setSuccess(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : t('errors.resetPasswordFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <LogoBackground>
      <AuthThemeToggle />
      <div className="w-full max-w-md mx-auto px-4">
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="h-16 w-16">
              <LogoComponent to="/" height="64px" width="64px" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-soccer-primary">{t('app.name')}</h1>
          <p className={`mt-2 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>{t('app.tagline')}</p>
        </div>

        <Card className={`border-0 shadow-xl overflow-hidden ${theme === 'dark' ? 'bg-soccer-dark-200' : 'bg-[#2a3b4d]'}`}>
          <CardHeader>
            <CardTitle className="text-xl text-white">{t('auth.resetPassword')}</CardTitle>
          </CardHeader>
          <form onSubmit={handleResetPassword}>
            <CardContent className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert>
                  <AlertDescription>
                    {t('auth.resetPasswordSuccess', 'Password reset email sent. Please check your inbox.')}
                  </AlertDescription>
                </Alert>
              )}

              <p className="text-white text-sm">
                {t('auth.resetPasswordInstructions')}
              </p>

              <div className="space-y-2">
                <Label htmlFor="email" className="text-white">{t('auth.email')}</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="bg-soccer-dark-400 border-soccer-dark-600 text-white placeholder-gray-400"
                />
              </div>
            </CardContent>
            <CardFooter className="flex-col space-y-4">
              <Button
                type="submit"
                className="w-full bg-soccer-primary hover:bg-soccer-primary/90 text-white font-medium py-6"
                disabled={isLoading || success}
              >
                {isLoading ? t('common.loading') : t('auth.resetPassword')}
              </Button>

              <p className="text-sm text-center text-white">
                {t('auth.alreadyHaveAccount')}{" "}
                <Link to="/" className="text-soccer-primary hover:underline font-medium">
                  {t('auth.login')}
                </Link>
              </p>
            </CardFooter>
          </form>
        </Card>
      </div>
    </LogoBackground>
  );
};

export default ResetPasswordPage;
