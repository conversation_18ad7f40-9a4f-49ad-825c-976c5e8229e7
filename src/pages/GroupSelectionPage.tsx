import { useEffect, useState, useRef } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/context/AuthContext';
import { useGroup } from '@/lib/context/GroupContext';
import { Loader2, Plus, Trash2, UserCircle, Users, Share2, UserPlus, UserCog, Edit, Upload, FileJson, Download } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import ShareManagerWithErrorBoundary from '@/components/sharing/ShareManagerWithErrorBoundary';
import { useTheme } from '@/context/ThemeContext';
import ThemeToggle from '@/components/ThemeToggle';
import LogoComponent from '@/components/LogoComponent';
import MemberManager from '@/components/groups/MemberManager';
import ImportPreview from '@/components/groups/ImportPreview';
// Name-based import is now integrated into the standard import
import ExportGroupData from '@/components/groups/ExportGroupData';
import { fetchUserGroups, Group as GroupType, updateGroupName, checkUserPermission, createGroupWithImport } from '@/lib/api/groups';
import { validateImportData, ImportData } from '@/lib/validators/importSchema';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Using the Group type from the API
type Group = GroupType;

const GroupSelectionPage = () => {
  const [groups, setGroups] = useState<Group[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [groupName, setGroupName] = useState('');
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);
  const { user } = useAuth();
  const { setCurrentGroup } = useGroup();

  // State for member management
  const [isMemberModalOpen, setIsMemberModalOpen] = useState(false);
  const [activeGroupForMembers, setActiveGroupForMembers] = useState<string | null>(null);

  // State for editing group name
  const [isEditNameModalOpen, setIsEditNameModalOpen] = useState(false);
  const [editingGroup, setEditingGroup] = useState<Group | null>(null);
  const [newGroupName, setNewGroupName] = useState('');
  const [isUpdatingName, setIsUpdatingName] = useState(false);

  // State for import functionality
  const [createMode, setCreateMode] = useState<'empty' | 'import'>('empty');
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importData, setImportData] = useState<ImportData | null>(null);
  const [isImporting, setIsImporting] = useState(false);
  const [importError, setImportError] = useState<string | null>(null);
  const [showImportPreview, setShowImportPreview] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const loadGroups = async () => {
      if (!user?.id) return;

      setLoading(true);
      try {
        // Use the enhanced API function to fetch groups
        const data = await fetchUserGroups(user.id);
        setGroups(data || []);
      } catch (error: any) {
        console.error('Error fetching groups:', error);
        toast({
          title: 'Error',
          description: error?.message || 'Failed to fetch groups.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    loadGroups();
  }, [toast, user?.id]);

  const handleGroupSelect = (groupId: string) => {
    // Find the group object from the groups array
    const group = groups.find(g => g.id === groupId);
    if (group) {
      // Update the context state with the selected group
      setCurrentGroup(group);
      // Also update localStorage for persistence
      localStorage.setItem('selectedGroupId', groupId);
    }
    navigate('/dashboard');
  };

  const handleCreateGroup = () => {
    setGroupName('');
    setIsModalOpen(true);
  };

  const handleCreateGroupSubmit = async () => {
    try {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      // If we're in import mode and have import data, use the import function
      if (createMode === 'import' && importData) {
        setIsImporting(true);
        const data = await createGroupWithImport(groupName, user.id, importData);

        if (data && data.length > 0) {
          // Ensure we have the complete group object
          const newGroup: Group = {
            id: data[0].id,
            name: data[0].name,
            created_by: data[0].created_by,
            created_at: data[0].created_at
          };

          // Update the groups state with the new group
          setGroups(prevGroups => [...prevGroups, newGroup]);

          // Automatically select the newly created group
          setCurrentGroup(newGroup);
          localStorage.setItem('selectedGroupId', newGroup.id);

          console.log('New group with imported data added to state and selected:', newGroup);
        }
      } else {
        // Regular empty group creation
        const { data, error } = await supabase
          .from('friend_groups')
          .insert([{
            name: groupName,
            created_by: user.id
          }])
          .select();

        if (error) throw error;

        if (data && data.length > 0) {
          // Ensure we have the complete group object
          const newGroup: Group = {
            id: data[0].id,
            name: data[0].name,
            created_by: data[0].created_by,
            created_at: data[0].created_at
          };

          // Update the groups state with the new group
          setGroups(prevGroups => [...prevGroups, newGroup]);

          // Automatically select the newly created group
          setCurrentGroup(newGroup);
          localStorage.setItem('selectedGroupId', newGroup.id);

          console.log('New empty group added to state and selected:', newGroup);
        } else {
          console.warn('No data returned from insert operation');
        }
      }

      // Reset all state
      setIsModalOpen(false);
      setGroupName('');
      setCreateMode('empty');
      setImportFile(null);
      setImportData(null);
      setShowImportPreview(false);
      setIsImporting(false);
      setImportError(null);
      if (fileInputRef.current) fileInputRef.current.value = '';

      toast({ title: 'Success', description: 'Group created successfully.' });
    } catch (error: any) {
      console.error('Error creating group:', error);
      setIsImporting(false);
      toast({
        title: 'Error',
        description: error?.message || 'Failed to create group.',
        variant: 'destructive'
      });
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    setImportError(null);
    setImportData(null);

    const file = e.target.files?.[0];
    if (!file) {
      setImportFile(null);
      return;
    }

    setImportFile(file);

    try {
      // Read and parse the file
      const fileContent = await file.text();
      const jsonData = JSON.parse(fileContent);

      // Validate the data structure
      const validatedData = validateImportData(jsonData);
      setImportData(validatedData);
      setShowImportPreview(true);
    } catch (error: any) {
      console.error('Error parsing import file:', error);
      setImportError(error.message || 'Invalid file format');
      setImportFile(null);
      if (fileInputRef.current) fileInputRef.current.value = '';
    }
  };

  const handleImportCancel = () => {
    setShowImportPreview(false);
    setImportData(null);
    setImportFile(null);
    setImportError(null);
    if (fileInputRef.current) fileInputRef.current.value = '';
  };

  const handleDeleteClick = (group: Group, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click event
    setSelectedGroup(group);
    setIsDeleteModalOpen(true);
  };

  const handleEditNameClick = (group: Group, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click event
    setEditingGroup(group);
    setNewGroupName(group.name);
    setIsEditNameModalOpen(true);
  };

  const handleUpdateGroupName = async () => {
    if (!editingGroup || !newGroupName.trim()) return;

    setIsUpdatingName(true);
    try {
      // Check if user has admin permission
      if (!user?.id) throw new Error('User not authenticated');

      const hasPermission = await checkUserPermission(editingGroup.id, user.id, 'Admin');
      if (!hasPermission && editingGroup.created_by !== user.id) {
        throw new Error('You need Admin permission to edit the group name');
      }

      // Update the group name
      const data = await updateGroupName(editingGroup.id, newGroupName);

      if (data && data.length > 0) {
        // Update the local state
        setGroups(prevGroups =>
          prevGroups.map(g =>
            g.id === editingGroup.id ? { ...g, name: newGroupName } : g
          )
        );

        // If this was the current group, update it in context
        const currentSelectedGroupId = localStorage.getItem('selectedGroupId');
        if (currentSelectedGroupId === editingGroup.id) {
          const updatedGroup = { ...editingGroup, name: newGroupName };
          setCurrentGroup(updatedGroup);
        }

        toast({ title: 'Success', description: 'Group name updated successfully.' });
      }
    } catch (error: any) {
      console.error('Error updating group name:', error);
      toast({
        title: 'Error',
        description: error?.message || 'Failed to update group name.',
        variant: 'destructive'
      });
    } finally {
      setIsUpdatingName(false);
      setIsEditNameModalOpen(false);
      setEditingGroup(null);
      setNewGroupName('');
    }
  };

  const handleDeleteGroup = async () => {
    if (!selectedGroup) return;

    setIsDeleting(true);
    try {
      const { error } = await supabase
        .from('friend_groups')
        .delete()
        .eq('id', selectedGroup.id);

      if (error) throw error;

      setGroups(prevGroups => prevGroups.filter(g => g.id !== selectedGroup.id));
      toast({ title: 'Success', description: 'Group deleted successfully.' });

      // If the deleted group was the selected one, clear it from localStorage
      const currentSelectedGroupId = localStorage.getItem('selectedGroupId');
      if (currentSelectedGroupId === selectedGroup.id) {
        localStorage.removeItem('selectedGroupId');
      }
    } catch (error: any) {
      console.error('Error deleting group:', error);
      toast({
        title: 'Error',
        description: error?.message || 'Failed to delete group.',
        variant: 'destructive'
      });
    } finally {
      setIsDeleting(false);
      setIsDeleteModalOpen(false);
      setSelectedGroup(null);
    }
  };

  const { theme } = useTheme();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-soccer-dark-100 transition-colors duration-200">
      {/* Header with profile link */}
      <header className="bg-soccer-primary text-white dark:bg-soccer-dark-200 py-4 transition-colors duration-200">
        <div className="container mx-auto flex justify-between items-center">
          <div className="flex items-center gap-2">
            <LogoComponent to="/dashboard" />
          </div>
          <div className="flex items-center gap-4">
            <ThemeToggle />
            <Link to="/profile" className="flex items-center gap-2 text-white hover:text-white/90">
              <UserCircle className="h-5 w-5" />
              Profile
            </Link>
          </div>
        </div>
      </header>

      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold dark:text-white">Select a Group</h2>
          <Button onClick={handleCreateGroup} className="bg-soccer-primary hover:bg-soccer-primary/90">
            <Plus className="h-4 w-4 mr-2" /> Create Group
          </Button>
        </div>

        {loading ? (
          <div className="flex items-center justify-center min-h-[300px]">
            <Loader2 className="h-8 w-8 animate-spin text-soccer-primary" />
            <span className="ml-2 text-lg dark:text-white">Loading groups...</span>
          </div>
        ) : groups.length === 0 ? (
          <div className="bg-white dark:bg-soccer-dark-300 rounded-lg shadow-sm p-8 text-center transition-colors duration-200">
            <Users className="h-12 w-12 mx-auto text-gray-400 dark:text-gray-500 mb-4" />
            <h3 className="text-xl font-medium mb-2 dark:text-white">No Groups Found</h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">You don't have any groups yet. Create your first group to get started.</p>
            <Button onClick={handleCreateGroup} className="bg-soccer-primary hover:bg-soccer-primary/90">
              <Plus className="h-4 w-4 mr-2" /> Create Your First Group
            </Button>
          </div>
        ) : (
          <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
            {groups.map((group) => (
              <Card key={group.id} className="overflow-hidden hover:shadow-md transition-shadow duration-200 dark:bg-soccer-dark-300 dark:border-soccer-dark-600">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg dark:text-white">{group.name}</CardTitle>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8" onClick={(e) => e.stopPropagation()}>
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <circle cx="12" cy="12" r="1" />
                            <circle cx="12" cy="5" r="1" />
                            <circle cx="12" cy="19" r="1" />
                          </svg>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            e.preventDefault();
                          }}
                          asChild
                        >
                          <div className="w-full">
                            <ShareManagerWithErrorBoundary groupId={group.id} groupName={group.name} />
                          </div>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={(e) => handleEditNameClick(group, e)}
                        >
                          <Edit className="h-4 w-4 mr-2" /> Edit Name
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            setActiveGroupForMembers(group.id);
                            setIsMemberModalOpen(true);
                          }}
                        >
                          <UserCog className="h-4 w-4 mr-2" /> Manage Members
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={(e) => e.stopPropagation()}
                          asChild
                        >
                          <div className="w-full flex items-center">
                            <ExportGroupData
                              groupId={group.id}
                              groupName={group.name}
                              variant="ghost"
                              size="sm"
                              className="w-full justify-start p-0 h-auto font-normal text-sm"
                            />
                          </div>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => handleDeleteClick(group, e)} className="text-red-600 focus:text-red-600">
                          <Trash2 className="h-4 w-4 mr-2" /> Delete Group
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">Created by: {user?.id === group.created_by ? 'You' : group.created_by}</p>
                  {group.created_at && (
                    <p className="text-xs text-gray-400 dark:text-gray-500">Created: {new Date(group.created_at).toLocaleDateString()}</p>
                  )}
                </CardContent>
                <CardFooter className="gap-2">
                  <Button variant="outline" className="w-full dark:bg-soccer-dark-400 dark:border-soccer-dark-600 dark:text-white dark:hover:bg-soccer-dark-500" onClick={() => handleGroupSelect(group.id)}>
                    Select Group
                  </Button>
                  <ExportGroupData
                    groupId={group.id}
                    groupName={group.name}
                    variant="secondary"
                    size="sm"
                    className="w-full text-xs"
                  />
                </CardFooter>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Create Group Modal */}
      <Dialog open={isModalOpen} onOpenChange={(open) => {
        if (!open) {
          // Reset state when closing
          setCreateMode('empty');
          setImportFile(null);
          setImportData(null);
          setShowImportPreview(false);
          setImportError(null);
          if (fileInputRef.current) fileInputRef.current.value = '';
        }
        setIsModalOpen(open);
      }}>
        <DialogContent className="dark:bg-gray-800 dark:border-gray-700 sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="dark:text-white">Create a New Group</DialogTitle>
            <DialogDescription className="dark:text-gray-300">
              Enter a name for your new group and choose how to set it up.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="group-name" className="dark:text-white">Group Name</Label>
              <Input
                id="group-name"
                placeholder="Enter group name"
                value={groupName}
                onChange={(e) => setGroupName(e.target.value)}
                className="dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400"
              />
            </div>

            {showImportPreview && importData ? (
              <ImportPreview
                importData={importData}
                onConfirm={handleCreateGroupSubmit}
                onCancel={handleImportCancel}
                isProcessing={isImporting}
              />
            ) : (
              <Tabs defaultValue="empty" onValueChange={(value) => setCreateMode(value as 'empty' | 'import')}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="empty">Empty Group</TabsTrigger>
                  <TabsTrigger value="import">Import Data</TabsTrigger>
                </TabsList>

                <TabsContent value="empty" className="pt-4">
                  <div className="text-sm text-muted-foreground">
                    Create an empty group and add players and matches later.
                  </div>
                </TabsContent>

                <TabsContent value="import" className="pt-4">
                  <div className="space-y-4">
                    <div className="text-sm text-muted-foreground mb-2">
                      Import existing players and matches from a JSON file.
                      <a
                        href="/sample-import.json"
                        target="_blank"
                        className="text-soccer-primary hover:underline ml-1"
                        onClick={(e) => e.stopPropagation()}
                      >
                        View sample format
                      </a>
                    </div>
                    <div className="text-xs text-muted-foreground mb-2">
                      Supports both index-based and name-based player references for reliable team assignments.
                    </div>

                    <div className="flex items-center justify-center w-full">
                      <label htmlFor="import-file" className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-gray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500">
                        <div className="flex flex-col items-center justify-center pt-5 pb-6">
                          {importFile ? (
                            <>
                              <FileJson className="w-8 h-8 mb-2 text-green-500 dark:text-green-400" />
                              <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">{importFile.name}</p>
                              <p className="text-xs text-gray-500 dark:text-gray-400">{Math.round(importFile.size / 1024)} KB</p>
                            </>
                          ) : (
                            <>
                              <Upload className="w-8 h-8 mb-2 text-gray-500 dark:text-gray-400" />
                              <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">Click to upload JSON file</p>
                              <p className="text-xs text-gray-500 dark:text-gray-400">Must contain players and matches</p>
                            </>
                          )}
                        </div>
                        <input
                          id="import-file"
                          type="file"
                          accept=".json,application/json"
                          className="hidden"
                          onChange={handleFileChange}
                          ref={fileInputRef}
                        />
                      </label>
                    </div>

                    {importError && (
                      <div className="text-sm text-red-500 dark:text-red-400 mt-2">
                        {importError}
                      </div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            )}
          </div>

          <DialogFooter>
            <Button
              onClick={handleCreateGroupSubmit}
              disabled={!groupName.trim() || (createMode === 'import' && !importData) || isImporting}
              className="bg-soccer-primary hover:bg-soccer-primary/90"
            >
              {isImporting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create Group'
              )}
            </Button>
            <Button variant="secondary" onClick={() => setIsModalOpen(false)} className="dark:bg-gray-700 dark:text-white">
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Group Confirmation Dialog */}
      <AlertDialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <AlertDialogContent className="dark:bg-soccer-dark-300 dark:border-soccer-dark-600">
          <AlertDialogHeader>
            <AlertDialogTitle className="dark:text-white">Delete Group</AlertDialogTitle>
            <AlertDialogDescription className="dark:text-gray-300">
              Are you sure you want to delete the group "{selectedGroup?.name}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="dark:bg-soccer-dark-400 dark:text-white dark:hover:bg-soccer-dark-500">Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteGroup} className="bg-red-600 hover:bg-red-700">
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>Delete</>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Member Management Modal */}
      <Dialog open={isMemberModalOpen} onOpenChange={setIsMemberModalOpen}>
        <DialogContent className="sm:max-w-2xl dark:bg-gray-800 dark:border-gray-700">
          <DialogHeader>
            <DialogTitle className="dark:text-white">
              Manage Group Members
            </DialogTitle>
            <DialogDescription className="dark:text-gray-300">
              Add, remove, or change roles of members in this group.
            </DialogDescription>
          </DialogHeader>
          {activeGroupForMembers && (
            <MemberManager
              groupId={activeGroupForMembers}
              isCreator={user?.id === groups.find(g => g.id === activeGroupForMembers)?.created_by}
              onMembersChange={() => {
                // Refresh groups list after member changes
                if (user?.id) {
                  fetchUserGroups(user.id).then(data => setGroups(data || []));
                }
              }}
            />
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsMemberModalOpen(false)}
              className="dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Group Name Modal */}
      <Dialog open={isEditNameModalOpen} onOpenChange={setIsEditNameModalOpen}>
        <DialogContent className="sm:max-w-md dark:bg-gray-800 dark:border-gray-700">
          <DialogHeader>
            <DialogTitle className="dark:text-white">
              Edit Group Name
            </DialogTitle>
            <DialogDescription className="dark:text-gray-300">
              Change the name of your group.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-group-name" className="dark:text-white">Group Name</Label>
              <Input
                id="edit-group-name"
                placeholder="Enter new group name"
                value={newGroupName}
                onChange={(e) => setNewGroupName(e.target.value)}
                className="dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              onClick={handleUpdateGroupName}
              disabled={!newGroupName.trim() || isUpdatingName}
              className="bg-soccer-primary hover:bg-soccer-primary/90"
            >
              {isUpdatingName ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                'Update Name'
              )}
            </Button>
            <Button
              variant="outline"
              onClick={() => setIsEditNameModalOpen(false)}
              className="dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default GroupSelectionPage;