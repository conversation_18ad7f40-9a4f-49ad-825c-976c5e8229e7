import { useState } from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/context/AuthContext";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Checkbox } from "@/components/ui/checkbox";
import { FaGoogle } from "react-icons/fa";
import AuthThemeToggle from "@/components/auth/AuthThemeToggle";
import LanguageToggle from "@/components/LanguageToggle";
import { useTheme } from "@/context/ThemeContext";
import LogoComponent from "@/components/LogoComponent";
import LogoBackground from "@/components/auth/LogoBackground";

const LoginPage = () => {
  const { t } = useTranslation();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { login, signInWithGoogle } = useAuth();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    try {
      await login(email, password, rememberMe);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : t('errors.invalidCredentials');

      // Check if the error is related to email confirmation
      if (errorMessage.includes("Email not confirmed") || errorMessage.includes("not confirmed")) {
        setError(t('errors.emailNotConfirmed', 'Please check your email and confirm your account before logging in.'));
      } else {
        setError(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    setError(null);
    setIsLoading(true);

    try {
      await signInWithGoogle();
    } catch (err) {
      setError(err instanceof Error ? err.message : t('errors.socialLoginFailed', { provider: 'Google' }));
    } finally {
      setIsLoading(false);
    }
  };

  const { theme } = useTheme();

  return (
    <LogoBackground>
      <AuthThemeToggle />
      <div className="w-full max-w-md mx-auto px-4">
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="h-16 w-16">
              <LogoComponent to="/dashboard" height="64px" width="64px" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-soccer-primary">{t('app.name')}</h1>
          <p className={`mt-2 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>{t('app.tagline')}</p>
        </div>

        <Card className={`border-0 shadow-xl overflow-hidden ${theme === 'dark' ? 'bg-soccer-dark-200' : 'bg-card'}`}>
          <CardHeader>
            <CardTitle className={`text-xl ${theme === 'dark' ? 'text-white' : 'text-card-foreground'}`}>{t('auth.login')}</CardTitle>
          </CardHeader>
          <form onSubmit={handleLogin}>
            <CardContent className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <Label htmlFor="email" className={theme === 'dark' ? 'text-white' : 'text-card-foreground'}>{t('auth.email')}</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className={theme === 'dark' ? 'bg-soccer-dark-400 border-soccer-dark-600 text-white placeholder-gray-400' : 'bg-background border-input text-foreground placeholder-gray-400'}
                />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="password" className={theme === 'dark' ? 'text-white' : 'text-card-foreground'}>{t('auth.password')}</Label>
                  <Link
                    to="/reset-password"
                    className="text-xs text-soccer-primary hover:underline"
                  >
                    {t('auth.forgotPassword')}
                  </Link>
                </div>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className={theme === 'dark' ? 'bg-soccer-dark-400 border-soccer-dark-600 text-white' : 'bg-background border-input text-foreground'}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="rememberMe"
                  checked={rememberMe}
                  onCheckedChange={(checked) => setRememberMe(checked as boolean)}
                />
                <Label htmlFor="rememberMe" className={`text-sm ${theme === 'dark' ? 'text-white' : 'text-card-foreground'}`}>
                  {t('auth.rememberMe')}
                </Label>
              </div>
            </CardContent>
            <CardFooter className="flex-col space-y-4">
              <Button
                type="submit"
                className="w-full bg-soccer-primary hover:bg-soccer-primary/90 text-white font-medium py-6"
                disabled={isLoading}
              >
                {isLoading ? t('common.loading') : t('auth.login')}
              </Button>

              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className={`w-full border-t ${theme === 'dark' ? 'border-gray-600' : 'border-gray-300'}`} />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className={`px-2 text-gray-400 ${theme === 'dark' ? 'bg-soccer-dark-200' : 'bg-card'}`}>{t('common.or')}</span>
                </div>
              </div>

              <div className="flex justify-center">
                <Button
                  type="button"
                  variant="outline"
                  className={`w-full max-w-xs ${theme === 'dark' ? 'bg-soccer-dark-300 border-soccer-dark-600 text-white hover:bg-soccer-dark-500' : 'bg-background border-input text-foreground hover:bg-accent'}`}
                  onClick={handleGoogleLogin}
                  disabled={isLoading}
                >
                  <FaGoogle className="mr-2" />
                  Google
                </Button>
              </div>

              <p className={`text-sm text-center ${theme === 'dark' ? 'text-white' : 'text-card-foreground'}`}>
                {t('auth.noAccount')}{" "}
                <Link to="/signup" className="text-soccer-primary hover:underline font-medium">
                  {t('auth.signup')}
                </Link>
              </p>
            </CardFooter>
          </form>
        </Card>
      </div>
    </LogoBackground>
  );
};

export default LoginPage;
